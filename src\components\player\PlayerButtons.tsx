import { View } from "~/components/View";
import { NextIcon } from "~/components/icons/Next";
import { Next2Icon } from "~/components/icons/Next2";
import { PlayIcon } from "~/components/icons/Play";
import { PrevIcon } from "~/components/icons/Prev";
import { Prev2Icon } from "~/components/icons/Prev2";
import { PauseIcon } from "~/components/icons/Pause";
import { ZoomIn } from "lucide-react";
import { cn } from "~/lib/utils";

export const PlayerButtons = ({
  currentFrame,
  isReading,
  isPaused,
  onPlayPauseVideo,
  onPrevFrame,
  onNextFrame,
  isZoomMode,
  onToggleZoomMode,
}: {
  currentFrame: number;
  isReading: boolean;
  isPaused?: boolean;
  onPlayPauseVideo: () => void;
  onPrevFrame: (number?: number) => void;
  onNextFrame: (number?: number) => void;
  isZoomMode?: boolean;
  onToggleZoomMode?: () => void;
}) => {
  return (
    <div className="flex gap-[5px]">
      <View
        hotkeys={["Shift", "←"]}
        description="prev. 10"
        className="translate-x-[-180%]"
      >
        <button
          className="rounded-full border p-2"
          disabled={currentFrame <= 1 || isReading || isZoomMode}
          onClick={() => onPrevFrame(10)}
        >
          <Prev2Icon />
        </button>
      </View>
      <View
        hotkeys={["←"]}
        description="prev. frame"
        className="w-24 translate-x-[-110%]"
      >
        <button
          className="rounded-full border p-2"
          disabled={currentFrame <= 1 || isReading || isZoomMode}
          onClick={() => onPrevFrame()}
        >
          <PrevIcon />
        </button>
      </View>
      <View
        hotkeys={["spacebar"]}
        description="play/pause"
        className="w-24 translate-x-[-50%]"
      >
        <button
          className="aspect-square rounded-full border p-2"
          disabled={isZoomMode}
          onClick={onPlayPauseVideo}
        >
          {isPaused ? <PlayIcon /> : <PauseIcon />}
        </button>
      </View>
      <View
        hotkeys={["→"]}
        description="next frame"
        className="w-24 translate-x-[10%]"
      >
        <button
          className="rounded-full border p-2"
          disabled={isZoomMode}
          onClick={() => onNextFrame()}
        >
          <NextIcon />
        </button>
      </View>
      <View
        hotkeys={["Shift", "→"]}
        description="next 10"
        className="w-24 translate-x-[70%]"
      >
        <button
          className="rounded-full border p-2"
          disabled={isZoomMode}
          onClick={() => onNextFrame(10)}
        >
          <Next2Icon />
        </button>
      </View>
      {onToggleZoomMode && (
        <View
          hotkeys={["z"]}
          description="magnifier"
          className="w-24 translate-x-[130%]"
        >
          <button
            className={cn(
              "rounded-full border p-2",
              isZoomMode && "border-blue-500 bg-blue-100",
            )}
            onClick={onToggleZoomMode}
          >
            <ZoomIn className="h-3 w-3" />
          </button>
        </View>
      )}
    </div>
  );
};
