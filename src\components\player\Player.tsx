"use client";

import { useEffect, useRef, useState } from "react";
import toast from "react-hot-toast";
import { useHotkeys } from "react-hotkeys-hook";
import videojs from "video.js";
import type Player from "video.js/dist/types/player";
import "video.js/dist/video-js.css";
import { Card } from "~/components/Card";
import { Input } from "~/components/ui/input";
import { Progress } from "~/components/ui/progress";
import { View } from "~/components/View";
import { useStore } from "~/hooks/store";
import { useRunOnce } from "~/hooks/useEffectOnce";
import { extractFrames, type TSSegment } from "~/lib/ffmpeg";
import { getSegmentsByFrameNumber } from "~/lib/m3u8";
import { PlayerButtons } from "./PlayerButtons";
import { VideoTimeline } from "./VideoTimeline";
import { cn, getNextTag, getPrevTag } from "~/lib/utils";
import { FpsEditor } from "./FpsEditor";
import type { GetPoseInfoOutput } from "~/server/api/routers/pose";
import type { M3u8Segment, TagUI } from "~/lib/interface";
import type {
  PartialBodyKeypoints,
  PartialBodyAngles,
  ColouredLine,
  DrawingMode,
  LineColor,
  PoseFrameMap,
} from "~/lib/interfaces/drawingTypes";
import { CanvasManager } from "./CanvasManager";
import type { BodyKeypoint, BodyAngle } from "~/server/db/schema";
import { PoseControlMenu } from "./PoseDropdown";

export const VideoPlayer = ({
  m3u8Segments,
  m3u8Text,
  tagTypes,
  poseData,
}: {
  m3u8Segments: M3u8Segment[];
  m3u8Text: string;
  tagTypes: TagUI[];
  poseData: GetPoseInfoOutput;
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const playerRef = useRef<Player | null>(null);
  const frameInputRef = useRef<HTMLInputElement>(null);
  const playerContainerRef = useRef<HTMLDivElement>(null);
  const animationFrameId = useRef<number | null>(null);

  const currentFrame = useStore((state) => state.currentFrame);
  const selectedAthlete = useStore((state) => state.selectedAthlete);
  const filteredTags = useStore((state) => state.filteredTags);
  const editingTagId = useStore((state) => state.editingTagId);
  const videoSummary = useStore((state) => state.videoSummary);
  const setCurrentFrame = useStore((state) => state.setCurrentFrame);
  const setEditingTagId = useStore((state) => state.setEditingTagId);
  const [isTimelineVisible, setTimelineVisible] = useState(true);

  const [isCtrlPressed, setIsCtrlPressed] = useState(false);
  const [firstSegment, setFirstSegment] = useState<TSSegment>();
  const [duration, setDuration] = useState(0);
  const [isReading, setIsReading] = useState(false);
  const [playerSize, setPlayerSize] = useState<{
    width: number;
    height: number;
  }>({ width: 0, height: 0 });
  const [videoAspectRatio, setVideoAspectRatio] = useState(16 / 9);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showPose, setShowPose] = useState(true);
  const [selectedColour, setSelectedColour] = useState<LineColor>("red");
  const [drawingMode, setDrawingMode] = useState<DrawingMode>("line");
  const [lines, setLines] = useState<ColouredLine[]>([]);
  const [hotkeyCd, setHotkeyCd] = useState(false);
  const [poseDataByFrame, setPoseDataByFrame] = useState<PoseFrameMap>({
    keypoints: new Map<number, BodyKeypoint[]>(),
    angles: new Map<number, BodyAngle[]>(),
  });
  const [strokeWidth, setStrokeWidth] = useState<number>(2);

  const [isZoomMode, setIsZoomMode] = useState(false);
  const [zoomState, setZoomState] = useState({
    scale: 1,
    offsetX: 0,
    offsetY: 0,
  });

  const nextTag = getNextTag(currentFrame, filteredTags, editingTagId);
  const prevTag = getPrevTag(currentFrame, filteredTags, editingTagId);

  const fps = firstSegment?.fps;
  const totalFrames = duration * (fps ?? 0);

  const handleUndoLine = () => {
    setLines((x) => x.slice(0, -1));
  };

  const handleClearLines = () => {
    setLines([]);
  };

  useEffect(() => {
    if (hotkeyCd) {
      setTimeout(() => {
        setHotkeyCd(false);
      }, 10);
    }
  }, [hotkeyCd]);

  // Process pose data into frame-indexed lookup maps when it loads
  useEffect(() => {
    if (poseData) {
      // Create frame-indexed maps for fast lookup
      const keypointsByFrame = new Map<number, PartialBodyKeypoints>();
      const anglesByFrame = new Map<number, PartialBodyAngles>();

      // Index keypoints by frame number
      poseData.keypoints.forEach((keypoint) => {
        if (!keypointsByFrame.has(keypoint.frameNumber)) {
          keypointsByFrame.set(keypoint.frameNumber, []);
        }
        keypointsByFrame.get(keypoint.frameNumber)!.push(keypoint);
      });

      // Index angles by frame number
      poseData.angles.forEach((angle) => {
        if (!anglesByFrame.has(angle.frameNumber)) {
          anglesByFrame.set(angle.frameNumber, []);
        }
        anglesByFrame.get(angle.frameNumber)!.push(angle);
      });

      // Set the indexed data structure
      setPoseDataByFrame({
        keypoints: keypointsByFrame,
        angles: anglesByFrame,
      });
    }
  }, [poseData]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Control") {
        setIsCtrlPressed(true);
      }
    };

    const handleKeyUp = (e: KeyboardEvent) => {
      if (e.key === "Control") {
        setIsCtrlPressed(false);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    window.addEventListener("keyup", handleKeyUp);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
      window.removeEventListener("keyup", handleKeyUp);
    };
  }, []);

  const currentFramePose = {
    keypoints: poseDataByFrame.keypoints.get(currentFrame) ?? [],
    angles: poseDataByFrame.angles.get(currentFrame) ?? [],
  };

  // Update player size on resize
  useEffect(() => {
    if (!playerContainerRef.current) return;

    const updateSize = () => {
      if (!playerContainerRef.current || isFullscreen) return;
      setPlayerSize({
        width: playerContainerRef.current.clientWidth,
        height: playerContainerRef.current.clientHeight,
      });
    };

    updateSize();
  }, [isFullscreen]);

  const {
    handleMouseDown: handleCanvasMouseDown,
    handleMouseMove: handleCanvasMouseMove,
    handleMouseUp: handleCanvasMouseUp,
  } = CanvasManager({
    canvasRef,
    currentFrame,
    filteredTags,
    selectedAthlete: selectedAthlete ?? "",
    showPose,
    currentFramePose,
    firstSegment,
    lines,
    setLines,
    drawingMode,
    selectedColour,
    isCtrlPressed,
    strokeWidth,
  });

  // Mouse event handlers for drawing
  const handleMouseDown = (e: React.MouseEvent<HTMLCanvasElement>) => {
    handleCanvasMouseDown(e);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLCanvasElement>) => {
    handleCanvasMouseMove(e);
  };

  const handleMouseUp = () => {
    handleCanvasMouseUp();
  };

  // New zoom functionality
  const handleVideoClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isZoomMode) return;

    // Prevent default behavior
    e.preventDefault();
    e.stopPropagation();

    const overlay = e.currentTarget;
    const rect = overlay.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const clickY = e.clientY - rect.top;

    // Calculate the center point for zoom (50% zoom in)
    const newScale = zoomState.scale === 1 ? 2 : 1;

    if (newScale === 1) {
      // Reset zoom
      setZoomState({ scale: 1, offsetX: 0, offsetY: 0 });
    } else {
      // Zoom in at click position
      const centerX = rect.width / 2;
      const centerY = rect.height / 2;
      const offsetX = (centerX - clickX) * 0.5;
      const offsetY = (centerY - clickY) * 0.5;

      setZoomState({ scale: newScale, offsetX, offsetY });
    }

    // Exit zoom mode after clicking
    setIsZoomMode(false);
  };

  const toggleZoomMode = () => {
    setIsZoomMode(!isZoomMode);
  };

  const getVideoTransformStyle = (): React.CSSProperties => {
    return {
      transform: `translate(${zoomState.offsetX}px, ${zoomState.offsetY}px) scale(${zoomState.scale})`,
      transformOrigin: "center center",
      cursor: isZoomMode ? "zoom-in" : "default",
      pointerEvents: isZoomMode ? "none" : "auto",
    };
  };

  //load first ts chunk
  useRunOnce(() => {
    //get info of first segment
    const getFirstSegmentInfo = async () => {
      try {
        const { forwardSegments } = getSegmentsByFrameNumber({
          segments: m3u8Segments,
          frameNumber: currentFrame,
          fps: fps ?? 50,
        });
        setIsReading(true);
        const segment = forwardSegments[0]!;
        const frames = await extractFrames({
          videoUrl: segment.uri,
        });

        setFirstSegment(frames);
        setIsReading(false);
      } catch (error) {
        console.error(error);
        toast.error("Failed to load video segment");
      }
    };
    void getFirstSegmentInfo();
  });

  //load m3u8 for video js
  useRunOnce(() => {
    if (!videoRef.current) return;

    const blob = new Blob([m3u8Text], { type: "application/x-mpegURL" });
    const url = URL.createObjectURL(blob);

    const options = {
      autoplay: false,
      bigPlayButton: false,
      // Disable all progress and seeking controls
      controlBar: {
        progressControl: false,
        playToggle: true,
        volumePanel: true,
        fullscreenToggle: true,
        remainingTimeDisplay: true,
        currentTimeDisplay: true,
      },
      // muted: true,
      // controls: !isReading,
      controls: true,
      responsive: true,
      fluid: true,
      sources: [
        {
          src: url,
          type: "application/x-mpegURL",
        },
      ],
    };

    let player: Player;

    if (!playerRef.current) {
      player = videojs(videoRef.current, options, () => {
        player.on("fullscreenchange", () => {
          // VideoJS has its own fullscreen API, this ensures we catch those events too
          const isPlayerFullscreen = player.isFullscreen() ?? false;
          setIsFullscreen(isPlayerFullscreen);
        });

        player.on("dispose", () => {
          videojs.log("player will dispose");
        });
      });

      player.on("error", () => {
        const error = player.error();
        console.error("Video.js Error:", error);
      });

      playerRef.current = player;
    } else {
      player = playerRef.current;
      player.src(url);
    }
  });

  const onVideoTimeUpdate = () => {
    if (!firstSegment) return;
    const video = videoRef.current;
    if (!video) return;

    const frameNumber = video.currentTime * (fps ?? 0) + 1;
    const frameNumberRounded = Math.floor(
      Math.round(frameNumber * 1000) / 1000,
    );
    if (!video.paused) {
      setCurrentFrame(frameNumberRounded);
    }
  };

  const startFrameUpdates = () => {
    if (!videoRef.current || !firstSegment) return;

    const updateFrame = () => {
      // Only update if the video is playing
      if (videoRef.current && !videoRef.current.paused) {
        onVideoTimeUpdate();
        animationFrameId.current = requestAnimationFrame(updateFrame);
      } else {
        stopFrameUpdates();
      }
    };

    animationFrameId.current = requestAnimationFrame(updateFrame);
  };

  const stopFrameUpdates = () => {
    if (animationFrameId.current !== null) {
      cancelAnimationFrame(animationFrameId.current);
      animationFrameId.current = null;
    }
  };

  useEffect(() => {
    const player = playerRef.current;
    if (!player) return;

    const handlePlay = () => {
      startFrameUpdates();
    };

    const handlePause = () => {
      stopFrameUpdates();
      // This ensures we get the final frame
      setTimeout(() => {
        onVideoTimeUpdate();
      }, 50);
    };

    player.on("play", handlePlay);
    player.on("pause", handlePause);

    // Start updates if video is already playing
    if (player.paused() === false) {
      startFrameUpdates();
    }

    return () => {
      player.off("play", handlePlay);
      player.off("pause", handlePause);
      stopFrameUpdates();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [firstSegment]);

  //when video time updates, update current frame

  const playerElement = playerRef.current;

  const onFrameNumberChange = (value: string) => {
    if (!currentFrame || !fps) return;
    const video = videoRef.current;
    if (!video) return;
    const newFrame = Math.round(+value);
    setCurrentFrame(newFrame);

    if (video.paused) {
      let newCurrentTime = (newFrame - 1) / fps;
      newCurrentTime += 0.00001;
      video.currentTime = newCurrentTime;
    }
  };

  const onNextFrame = (number = 1) => {
    playerElement?.pause();
    onFrameNumberChange((currentFrame + number).toString());
  };

  const onPrevFrame = (number = 1) => {
    if (currentFrame <= 1) return;
    playerElement?.pause();
    let targetFrame = currentFrame - number;
    if (targetFrame < 1) targetFrame = 1;
    onFrameNumberChange(targetFrame.toString());
  };

  const onNextTag = () => {
    if (!nextTag) return;
    if (!playerElement?.paused()) {
      playerElement?.pause();
    }
    onFrameNumberChange(nextTag.frame.toString());
    setEditingTagId(nextTag.id);
  };

  const onPrevTag = () => {
    if (!prevTag) return;
    playerElement?.pause();
    onFrameNumberChange(prevTag.frame.toString());
    setEditingTagId(prevTag.id);
  };

  const onPlayPauseVideo = () => {
    if (isReading) return;
    const player = playerRef.current;
    if (!player) return;
    if (player.paused()) {
      void player.play();
    } else {
      player.pause();
    }
  };

  const handleProgressBarClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const progressBar = event.currentTarget;
    const clickPosition =
      event.clientX - progressBar.getBoundingClientRect().left;
    const progressBarWidth = progressBar.offsetWidth;
    const percentage = clickPosition / progressBarWidth;
    const player = playerRef.current;
    if (player && duration) {
      player.currentTime(percentage * duration);
    }
  };

  // Calculate dimensions to fit video in container while preserving aspect ratio
  const calculatePlayerDimensions = () => {
    const containerWidth = playerSize.width;
    const containerHeight = playerSize.height;
    // If container is wider than video aspect ratio
    if (containerWidth / containerHeight > videoAspectRatio) {
      // Height is limiting factor
      const height = containerHeight;
      const width = height * videoAspectRatio;
      return { width, height };
    } else {
      // Width is limiting factor
      const width = containerWidth;
      const height = width / videoAspectRatio;
      return { width, height };
    }
  };

  const playerDimensions = calculatePlayerDimensions();

  const onHotKeyPress = (func: () => void) => {
    if (hotkeyCd) return;
    setHotkeyCd(true);
    func();
  };

  useHotkeys("left", () => onHotKeyPress(onPrevFrame));
  useHotkeys("shift+left", () => onHotKeyPress(() => onPrevFrame(10)));
  useHotkeys("ctrl+left", () => onHotKeyPress(onPrevTag));
  useHotkeys("right", () => onHotKeyPress(onNextFrame));
  useHotkeys("shift+right", () => onHotKeyPress(() => onNextFrame(10)));
  useHotkeys("ctrl+right", () => onHotKeyPress(onNextTag));
  useHotkeys("esc", () => setEditingTagId(null));
  useHotkeys("space", onPlayPauseVideo);
  useHotkeys("ctrl+z", () => handleUndoLine());
  useHotkeys("z", () => toggleZoomMode());

  useHotkeys(
    "ctrl+q",
    () => {
      setTimelineVisible(!isTimelineVisible);
    },
    [isTimelineVisible],
  );

  useEffect(() => {
    return () => {
      stopFrameUpdates();
    };
  }, []);

  return (
    <div className="col-span-9 flex h-full flex-col gap-2.5">
      <Card className="relative flex min-h-0 flex-1 flex-col gap-[5px]">
        <div
          ref={playerContainerRef}
          className={cn(
            "flex min-h-0 flex-1 items-center justify-center overflow-hidden bg-seaSalt-60",
            isTimelineVisible ? "" : "h-[calc(100%+166px)]",
          )}
        >
          <div
            className={cn(
              "relative max-h-full max-w-full",
              !isTimelineVisible && "w-full",
            )}
            style={{
              width: isTimelineVisible ? playerDimensions.width : undefined,
              height: isTimelineVisible ? playerDimensions.height : undefined,
            }}
          >
            <video
              id="video-player"
              ref={videoRef}
              style={getVideoTransformStyle()}
              onLoadedMetadata={(e) => {
                setDuration(e.currentTarget.duration);
                if (e.currentTarget.videoWidth && e.currentTarget.videoHeight) {
                  setVideoAspectRatio(
                    e.currentTarget.videoWidth / e.currentTarget.videoHeight,
                  );
                }
              }}
              className="video-js h-full w-full"
              playsInline
              onTimeUpdate={onVideoTimeUpdate}
              onPause={() => {
                //This is a bit hacky, do this because setState is fake sync. We want to call nextFrame after state is updated
                setTimeout(() => {
                  onNextFrame();
                }, 300);
              }}
            />
            {isZoomMode && (
              <div
                className="absolute inset-0 z-10 cursor-zoom-in"
                onClick={handleVideoClick}
              />
            )}
            <canvas
              ref={canvasRef}
              className={cn(
                "absolute left-0 top-0 h-full w-full",
                isCtrlPressed ? "pointer-events-auto" : "pointer-events-none",
              )}
              style={getVideoTransformStyle()}
              width={firstSegment?.width ?? playerDimensions.width}
              height={firstSegment?.height ?? playerDimensions.height}
              onMouseDown={handleMouseDown}
              onMouseMove={handleMouseMove}
              onMouseUp={handleMouseUp}
              onMouseLeave={handleMouseUp}
            />
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className="flex gap-5 text-smallLabel">
            <div className="grid grid-cols-2 gap-x-[5px] gap-y-1">
              <p>Current frame:</p>
              <p>{currentFrame}</p>
              <p>Current time:</p>
              <p>{((currentFrame - 1) / (fps ?? 1)).toFixed(3)}</p>
              {currentFramePose.keypoints.length > 0 && (
                <>
                  <p>Keypoints:</p>
                  <p>{currentFramePose.keypoints.length}</p>
                </>
              )}
            </div>
            {fps && <FpsEditor defaultFps={videoSummary?.fps ?? fps} />}
          </div>
          <PlayerButtons
            currentFrame={currentFrame}
            isReading={isReading}
            isPaused={playerRef.current?.paused()}
            onPlayPauseVideo={onPlayPauseVideo}
            onPrevFrame={onPrevFrame}
            onNextFrame={onNextFrame}
            isZoomMode={isZoomMode}
            onToggleZoomMode={toggleZoomMode}
          />

          <div className="flex flex-col gap-2.5">
            <div className="flex items-center gap-[2.5px]">
              <View
                hotkeys={["Ctrl", "→"]}
                description="next tag"
                className="w-24 translate-x-[-50%] translate-y-[50%]"
              >
                <p className="text-smallLabel">Go to frame:</p>
              </View>
              <View
                hotkeys={["Ctrl", "→"]}
                description="next tag"
                className="w-24 translate-x-[-20%] translate-y-[50%]"
              >
                <Input
                  placeholder="e.g. 200"
                  ref={frameInputRef}
                  disabled={isReading}
                  type="number"
                  className="text-smallLabel placeholder:text-smallLabel"
                  onFinish={(value) => {
                    onFrameNumberChange(value);
                    frameInputRef.current?.blur();
                  }}
                />
              </View>
            </div>
            <PoseControlMenu
              setShowPose={setShowPose}
              handleUndoLine={handleUndoLine}
              handleClearLines={handleClearLines}
              selectedColour={selectedColour}
              setSelectedColour={setSelectedColour}
              drawingMode={drawingMode}
              setDrawingMode={setDrawingMode}
              strokeWidth={strokeWidth}
              setStrokeWidth={setStrokeWidth}
            />
          </div>
        </div>
        <Progress
          value={(currentFrame / totalFrames) * 100}
          onClick={handleProgressBarClick}
          className="cursor-pointer"
        />
      </Card>

      {isTimelineVisible && (
        <div className="h-[166px] shrink-0">
          <VideoTimeline
            tagTypes={tagTypes}
            totalFrames={totalFrames}
            onFrameNumberChange={onFrameNumberChange}
            setEditingTagId={setEditingTagId}
          />
        </div>
      )}
    </div>
  );
};
